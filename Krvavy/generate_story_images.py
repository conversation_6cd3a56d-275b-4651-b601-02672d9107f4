#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from openai import OpenAI
import requests
from pathlib import Path
import time
import re

# Set your OpenAI API key
# Make sure to set this environment variable: export OPENAI_API_KEY="your-api-key"
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def sanitize_filename(filename):
    """Sanitize filename for safe file saving"""
    # Remove or replace problematic characters
    filename = re.sub(r'[🔪💀👻🎭🌙⚡️🔥💯🎪🎨🎬#]', '', filename)
    filename = re.sub(r'[^\w\s\-\(\)]', '', filename)
    filename = re.sub(r'\s+', '_', filename)
    filename = filename.strip('_')
    return filename

def create_horror_prompt(story_title):
    """Create a detailed prompt for horror story illustration"""

    # Base prompt template for retro fairytale horror illustrations
    base_prompt = """Stylized flat illustration in retro fairytale horror aesthetic. Limited color palette: parchment beige, blood red, dark brown, golden accents. Simplified geometric shapes, minimalist composition. Eastern European folklore style. Vector-style silhouettes only. NO TEXT.
    """
    
    # Specific prompts based on story titles
    story_prompts = {
        "Babylon 3:33": "Geometric ziggurat silhouette in dark brown, clock showing 3:33 in golden accents, mysterious symbols in blood red, minimalist composition with parchment background",

        "Newportský duch": "Simple lighthouse silhouette in dark brown, ghostly figure in blood red, harbor waves as geometric shapes, golden moon accent, folkloric maritime scene",

        "Perníková chalúpka": "Stylized gingerbread house silhouette in dark brown, candy decorations as blood red geometric shapes, twisted tree silhouettes, golden window glow",

        "🔪 Bezruká: Príbeh prekliateho mlyna": "Windmill silhouette in dark brown, broken blades as geometric shapes, armless figure silhouette in blood red, golden grain scattered symbolically",

        "Za starým dubom": "Ancient oak tree silhouette in dark brown, twisted branches as geometric patterns, mysterious shadow figure in blood red, golden leaves accent",
        
        "Slavošovský tunel": "Railway tunnel entrance as geometric arch in dark brown, train tracks as simple lines, mysterious light in golden accent, minimalist underground scene",

        "Niečo v rohu": "Room corner as geometric shapes in dark brown, mysterious shadow figure in blood red, simple furniture silhouettes, golden light beam accent",

        "Noku": "Japanese-inspired geometric house silhouette in dark brown, paper lantern in golden accent, mysterious figure in blood red, minimalist Eastern folklore",

        "Príbeh klaviristu Abrahama": "Grand piano silhouette in dark brown, musical notes as blood red geometric shapes, ghostly hands in golden accent, concert hall backdrop",

        "Škofja Loka, nacistami okupované": "Slovenian town silhouettes in dark brown, wartime symbols in blood red, fearful figures as simple shapes, golden church spire accent",
        
        "Entita - skutočný príbeh": "House silhouette in dark brown, floating objects as geometric shapes in blood red, mysterious orbs in golden accents, paranormal investigation theme",

        "Tisíc rytierov": "Knight silhouettes in dark brown, medieval weapons as geometric shapes in blood red, castle backdrop, golden crown accent, folkloric battle scene",

        "Hanička bez rúk": "Girl silhouette without arms in blood red, cottage doorway in dark brown, melancholic composition, golden light accent, tragic folklore",

        "Borievka - desivá rozprávka o": "Juniper tree silhouettes in dark brown, child figure in blood red, ravens as simple black shapes, golden berries accent, dark fairy tale",

        "Slnko, Mesiac a Tália (Šípková Ruženka)": "Castle silhouette in dark brown, sleeping princess in blood red, sun and moon symbols in golden accents, thorny vines as geometric patterns",
        
        "Malý chlapec": "Small boy silhouette in blood red, hallway as geometric shapes in dark brown, scattered toys as simple forms, golden light accent",

        "Démon v kapustnom poli": "Cabbage field as geometric rows in dark brown, demonic figure silhouette in blood red, harvest moon in golden accent, rural folklore",

        "Zberačka jahôd": "Woman silhouette in dark brown, strawberry basket in blood red, forest trees as geometric shapes, golden berries accent, folkloric scene",

        "Môj otec bol už raz ženatý...": "Family portrait frame in dark brown, ghostly figures in blood red, wedding elements as geometric shapes, golden ring accent",

        "Upír fekišovský": "Vampire silhouette in blood red, Slovak village houses in dark brown, church spire and bat shapes, golden moon accent, Eastern European folklore",
        
        "Horory zo Šíravy": "Slovak village houses as geometric shapes in dark brown, church bell tower silhouette, mysterious windows in blood red, golden bell accent",

        "Fotky smrti": "Vintage camera silhouette in dark brown, scattered photographs as geometric shapes, ghostly figures in blood red, golden flash accent",

        "Návrat zo záhrobia": "Cemetery tombstones as geometric shapes in dark brown, rising figure silhouette in blood red, golden cross accents, folkloric death theme",

        "Trebišovské horory": "Eastern Slovak buildings as geometric silhouettes in dark brown, mysterious figures in blood red, golden church dome accent",

        "Zmok (bez kostola a umývania)2": "Slavic dragon silhouette in blood red, cave entrance in dark brown, treasure as golden geometric shapes, folkloric beast",

        "Zmok (bez kostola a umývania)1": "Dragon silhouette from different angle in blood red, mountain peaks in dark brown, lightning as golden zigzag, Slavic mythology",

        "Jure Grando (1579 - 1656), u": "17th century vampire silhouette in blood red, Istrian stone houses in dark brown, coastal elements, golden moon accent",

        "Mužský narcizmus - Vlkolak": "Werewolf transformation silhouette in blood red, forest trees in dark brown, full moon in golden accent, psychological folklore",

        "Ako nám rozprávky pomáhajú": "Open book silhouette in dark brown, fairy tale elements in blood red, magical symbols in golden accents, storytelling theme",

        "Piatko a Pustaj": "Two contrasting figures in blood red, Slovak village houses in dark brown, moral duality symbols, golden accent details",

        "Zakliata hora": "Mountain peak silhouette in dark brown, cursed castle in blood red, swirling clouds as geometric shapes, golden lightning accent",

        "Mŕtvy frajer": "Young man's ghost silhouette in blood red, urban buildings in dark brown, streetlights as golden accents, contemporary folklore"
    }
    
    # Get specific prompt or use generic one
    specific_prompt = story_prompts.get(story_title, f"Dark atmospheric illustration inspired by the horror story '{story_title}', gothic mood, mysterious shadows, dramatic lighting")
    
    return f"{base_prompt}\n\nSpecific scene: {specific_prompt}"

def generate_image(prompt, filename, output_dir):
    """Generate image using OpenAI DALL-E API"""
    try:
        print(f"Generating image for: {filename}")
        print(f"Prompt: {prompt[:100]}...")
        
        response = client.images.generate(
            prompt=prompt,
            n=1,
            size="1024x1024",
            model="dall-e-3"
        )
        
        image_url = response.data[0].url
        
        # Download the image
        image_response = requests.get(image_url)
        if image_response.status_code == 200:
            filepath = os.path.join(output_dir, f"{filename}.png")
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Image saved: {filepath}")
            return True
        else:
            print(f"❌ Failed to download image for {filename}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating image for {filename}: {str(e)}")
        return False

def main():
    """Main function to generate all story images"""
    
    # Check if API key is set
    if not client.api_key:
        print("❌ Please set your OpenAI API key as environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Output directory
    output_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
    os.makedirs(output_dir, exist_ok=True)
    
    # List of stories to generate images for
    stories = [
        "Babylon 3:33",
        "Newportský duch", 
        "Perníková chalúpka",
        "🔪 Bezruká: Príbeh prekliateho mlyna",
        "Za starým dubom",
        "Slavošovský tunel",
        "Niečo v rohu",
        "Noku",
        "Príbeh klaviristu Abrahama",
        "Škofja Loka, nacistami okupované",
        "Entita - skutočný príbeh",
        "Tisíc rytierov",
        "Hanička bez rúk",
        "Borievka - desivá rozprávka o",
        "Slnko, Mesiac a Tália (Šípková Ruženka)",
        "Malý chlapec",
        "Démon v kapustnom poli",
        "Zberačka jahôd",
        "Môj otec bol už raz ženatý...",
        "Upír fekišovský",
        "Horory zo Šíravy",
        "Fotky smrti",
        "Návrat zo záhrobia",
        "Trebišovské horory",
        "Zmok (bez kostola a umývania)2",
        "Zmok (bez kostola a umývania)1", 
        "Jure Grando (1579 - 1656), u",
        "Mužský narcizmus - Vlkolak",
        "Ako nám rozprávky pomáhajú",
        "Piatko a Pustaj",
        "Zakliata hora",
        "Mŕtvy frajer"
    ]
    
    print(f"🎨 Starting image generation for {len(stories)} stories...")
    print(f"📁 Output directory: {output_dir}")
    
    successful = 0
    failed = 0
    
    for i, story in enumerate(stories, 1):
        print(f"\n[{i}/{len(stories)}] Processing: {story}")
        
        # Create prompt
        prompt = create_horror_prompt(story)
        
        # Sanitize filename
        safe_filename = sanitize_filename(story)
        
        # Generate image
        if generate_image(prompt, safe_filename, output_dir):
            successful += 1
        else:
            failed += 1
        
        # Add delay to respect API rate limits
        if i < len(stories):  # Don't wait after the last image
            print("⏳ Waiting 3 seconds before next generation...")
            time.sleep(3)
    
    print(f"\n🎉 Generation complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Images saved to: {output_dir}")

if __name__ == "__main__":
    main()
