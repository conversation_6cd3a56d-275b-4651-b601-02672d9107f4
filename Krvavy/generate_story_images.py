#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from openai import OpenAI
import requests
from pathlib import Path
import time
import re

# Set your OpenAI API key
# Make sure to set this environment variable: export OPENAI_API_KEY="your-api-key"
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def sanitize_filename(filename):
    """Sanitize filename for safe file saving"""
    # Remove or replace problematic characters
    filename = re.sub(r'[🔪💀👻🎭🌙⚡️🔥💯🎪🎨🎬#]', '', filename)
    filename = re.sub(r'[^\w\s\-\(\)]', '', filename)
    filename = re.sub(r'\s+', '_', filename)
    filename = filename.strip('_')
    return filename

def create_horror_prompt(story_title):
    """Create a detailed prompt for horror story illustration"""
    
    # Base prompt template for atmospheric horror illustrations
    base_prompt = """Create a dark, atmospheric horror illustration in the style of gothic art. 
    The image should be moody, mysterious, and evoke a sense of dread without being explicitly gory. 
    Use muted colors, dramatic shadows, and atmospheric lighting. 
    NO TEXT OR LETTERS should appear anywhere in the image.
    Style: Dark gothic art, atmospheric horror, cinematic lighting, detailed illustration.
    """
    
    # Specific prompts based on story titles
    story_prompts = {
        "Babylon 3:33": "Ancient Babylonian ruins at 3:33 AM, mysterious shadows moving between crumbling ziggurats, eerie moonlight casting long shadows, ancient symbols glowing faintly on weathered stone walls",
        
        "Newportský duch": "Misty harbor town at night, ghostly figure walking along empty pier, old lighthouse beam cutting through fog, Victorian-era buildings silhouetted against stormy sky",
        
        "Perníková chalúpka": "Dark twisted version of gingerbread house deep in haunted forest, candy decorations looking sinister in moonlight, gnarled trees surrounding the dwelling, ominous smoke rising from chimney",
        
        "🔪 Bezruká: Príbeh prekliateho mlyna": "Old abandoned windmill on hilltop, broken blades creaking in wind, dark storm clouds gathering, mysterious figure without arms standing in doorway",
        
        "Za starým dubom": "Ancient massive oak tree with twisted branches, mysterious shadows beneath its canopy, moonlight filtering through leaves, something lurking behind the trunk",
        
        "Slavošovský tunel": "Dark railway tunnel entrance, rusty tracks disappearing into blackness, overgrown vegetation, eerie light emanating from within the tunnel",
        
        "Niečo v rohu": "Dark corner of old room, shadows seeming to move and take shape, antique furniture covered in dust, single beam of light revealing something unsettling",
        
        "Noku": "Japanese-inspired horror scene, traditional house at night, paper lanterns casting eerie shadows, mysterious figure in traditional clothing",
        
        "Príbeh klaviristu Abrahama": "Old grand piano in abandoned concert hall, sheet music scattered on floor, ghostly hands hovering over keys, dramatic stage lighting",
        
        "Škofja Loka, nacistami okupované": "WWII-era Slovenian town under occupation, dark streets with Nazi symbols, fearful civilians hiding in shadows, oppressive atmosphere",
        
        "Entita - skutočný príbeh": "Paranormal investigation scene, old house with strange phenomena, floating objects, mysterious orbs of light, investigators with equipment",
        
        "Tisíc rytierov": "Medieval battlefield at dusk, ghostly knights in armor emerging from mist, ancient weapons scattered on ground, ethereal horses galloping",
        
        "Hanička bez rúk": "Young girl's silhouette without arms, standing in doorway of old cottage, melancholic atmosphere, soft tragic lighting",
        
        "Borievka - desivá rozprávka o": "Dark fairy tale forest, twisted juniper trees, pale child-like figure, ravens perched on branches, ominous fairy tale atmosphere",
        
        "Slnko, Mesiac a Tália (Šípková Ruženka)": "Sleeping Beauty's castle covered in thorns, princess lying in eternal sleep, sun and moon symbols, magical but dark atmosphere",
        
        "Malý chlapec": "Small boy's silhouette in dark hallway, toys scattered around, single light source creating long shadows, innocent yet unsettling mood",
        
        "Démon v kapustnom poli": "Vast cabbage field at night, demonic figure lurking between rows of vegetables, harvest moon overhead, rural horror setting",
        
        "Zberačka jahôd": "Woman picking strawberries in dark forest clearing, basket of red berries, mysterious shadows watching from trees, gothic rural scene",
        
        "Môj otec bol už raz ženatý...": "Old family portrait with mysterious elements, wedding photo with ghostly presence, vintage clothing, sepia tones with dark undertones",
        
        "Upír fekišovský": "Slovakian vampire in traditional setting, old village at night, gothic church in background, bat silhouettes against full moon",
        
        "Horory zo Šíravy": "Small Slovak village at night, traditional houses with dark secrets, church bell tower, mysterious lights in windows",
        
        "Fotky smrti": "Old photographs scattered on table, images showing ghostly figures, vintage camera, dark room with red light, paranormal photography",
        
        "Návrat zo záhrobia": "Cemetery at midnight, figure rising from grave, weathered tombstones, mist rolling across ground, gothic atmosphere",
        
        "Trebišovské horory": "Eastern Slovak town setting, old buildings with dark history, mysterious figures in traditional clothing, oppressive atmosphere",
        
        "Zmok (bez kostola a umývania)2": "Slavic dragon-like creature in dark cave, ancient beast with glowing eyes, treasure hoard, medieval fantasy horror",
        
        "Zmok (bez kostola a umývania)1": "Different angle of Slavic dragon, mountain lair, storm clouds, lightning illuminating massive creature",
        
        "Jure Grando (1579 - 1656), u": "17th century Istrian vampire, historical setting, old stone houses, Mediterranean coastal town at night",
        
        "Mužský narcizmus - Vlkolak": "Werewolf transformation scene, man becoming beast, full moon, forest setting, psychological horror elements",
        
        "Ako nám rozprávky pomáhajú": "Open fairy tale book with dark illustrations coming to life, magical but sinister atmosphere, storytelling elements",
        
        "Piatko a Pustaj": "Two mysterious figures in Slovak folklore setting, traditional village, one welcoming one forbidding, moral duality",
        
        "Zakliata hora": "Cursed mountain peak, dark clouds swirling around summit, mysterious castle or structure, foreboding landscape",
        
        "Mŕtvy frajer": "Young man's ghost in modern setting, urban environment, streetlights creating eerie shadows, contemporary horror atmosphere"
    }
    
    # Get specific prompt or use generic one
    specific_prompt = story_prompts.get(story_title, f"Dark atmospheric illustration inspired by the horror story '{story_title}', gothic mood, mysterious shadows, dramatic lighting")
    
    return f"{base_prompt}\n\nSpecific scene: {specific_prompt}"

def generate_image(prompt, filename, output_dir):
    """Generate image using OpenAI DALL-E API"""
    try:
        print(f"Generating image for: {filename}")
        print(f"Prompt: {prompt[:100]}...")
        
        response = client.images.generate(
            prompt=prompt,
            n=1,
            size="1024x1024",
            model="dall-e-3"
        )
        
        image_url = response.data[0].url
        
        # Download the image
        image_response = requests.get(image_url)
        if image_response.status_code == 200:
            filepath = os.path.join(output_dir, f"{filename}.png")
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Image saved: {filepath}")
            return True
        else:
            print(f"❌ Failed to download image for {filename}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating image for {filename}: {str(e)}")
        return False

def main():
    """Main function to generate all story images"""
    
    # Check if API key is set
    if not client.api_key:
        print("❌ Please set your OpenAI API key as environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Output directory
    output_dir = os.path.expanduser("~/Desktop/Nové_Obrázky_Príbehy")
    os.makedirs(output_dir, exist_ok=True)
    
    # List of stories to generate images for
    stories = [
        "Babylon 3:33",
        "Newportský duch", 
        "Perníková chalúpka",
        "🔪 Bezruká: Príbeh prekliateho mlyna",
        "Za starým dubom",
        "Slavošovský tunel",
        "Niečo v rohu",
        "Noku",
        "Príbeh klaviristu Abrahama",
        "Škofja Loka, nacistami okupované",
        "Entita - skutočný príbeh",
        "Tisíc rytierov",
        "Hanička bez rúk",
        "Borievka - desivá rozprávka o",
        "Slnko, Mesiac a Tália (Šípková Ruženka)",
        "Malý chlapec",
        "Démon v kapustnom poli",
        "Zberačka jahôd",
        "Môj otec bol už raz ženatý...",
        "Upír fekišovský",
        "Horory zo Šíravy",
        "Fotky smrti",
        "Návrat zo záhrobia",
        "Trebišovské horory",
        "Zmok (bez kostola a umývania)2",
        "Zmok (bez kostola a umývania)1", 
        "Jure Grando (1579 - 1656), u",
        "Mužský narcizmus - Vlkolak",
        "Ako nám rozprávky pomáhajú",
        "Piatko a Pustaj",
        "Zakliata hora",
        "Mŕtvy frajer"
    ]
    
    print(f"🎨 Starting image generation for {len(stories)} stories...")
    print(f"📁 Output directory: {output_dir}")
    
    successful = 0
    failed = 0
    
    for i, story in enumerate(stories, 1):
        print(f"\n[{i}/{len(stories)}] Processing: {story}")
        
        # Create prompt
        prompt = create_horror_prompt(story)
        
        # Sanitize filename
        safe_filename = sanitize_filename(story)
        
        # Generate image
        if generate_image(prompt, safe_filename, output_dir):
            successful += 1
        else:
            failed += 1
        
        # Add delay to respect API rate limits
        if i < len(stories):  # Don't wait after the last image
            print("⏳ Waiting 3 seconds before next generation...")
            time.sleep(3)
    
    print(f"\n🎉 Generation complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Images saved to: {output_dir}")

if __name__ == "__main__":
    main()
